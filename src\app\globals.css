@import "tailwindcss";

@theme {
  /* Primary brand colors */
  --color-primary-50: #faf5ff;
  --color-primary-100: #f3e8ff;
  --color-primary-200: #e9d5ff;
  --color-primary-300: #d8b4fe;
  --color-primary-400: #c084fc;
  --color-primary-500: #a855f7;
  --color-primary-600: #9333ea;
  --color-primary-700: #7c3aed;
  --color-primary-800: #6b21a8;
  --color-primary-900: #581c87;
  --color-primary-950: #3b0764;

  /* Secondary brand colors */
  --color-secondary-50: #fdf2f8;
  --color-secondary-100: #fce7f3;
  --color-secondary-200: #fbcfe8;
  --color-secondary-300: #f9a8d4;
  --color-secondary-400: #f472b6;
  --color-secondary-500: #ec4899;
  --color-secondary-600: #db2777;
  --color-secondary-700: #be185d;
  --color-secondary-800: #9d174d;
  --color-secondary-900: #831843;
  --color-secondary-950: #500724;

  /* Background colors */
  --color-bg-primary: #000000;
  --color-bg-secondary: #581c87;
  --color-bg-tertiary: #6b21a8;
  --color-bg-card: rgba(255, 255, 255, 0.05);
  --color-bg-card-hover: rgba(255, 255, 255, 0.1);
  --color-bg-card-active: rgba(255, 255, 255, 0.2);
  --color-bg-overlay: rgba(0, 0, 0, 0.8);

  /* Text colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #d1d5db;
  --color-text-tertiary: #9ca3af;
  --color-text-muted: #6b7280;

  /* Accent colors for game cards */
  --color-accent-warm-pink-from: #f472b6;
  --color-accent-warm-pink-to: #fbbf24;
  --color-accent-warm-red-from: #ef4444;
  --color-accent-warm-red-to: #7c3aed;
  --color-accent-warm-orange-from: #f97316;
  --color-accent-warm-orange-to: #dc2626;
  --color-accent-warm-yellow-from: #fbbf24;
  --color-accent-warm-yellow-to: #f97316;
  --color-accent-cool-blue-from: #2563eb;
  --color-accent-cool-blue-to: #6b21a8;
  --color-accent-cool-teal-from: #14b8a6;
  --color-accent-cool-teal-to: #1d4ed8;
  --color-accent-cool-indigo-from: #818cf8;
  --color-accent-cool-indigo-to: #9333ea;
  --color-accent-cool-cyan-from: #06b6d4;
  --color-accent-cool-cyan-to: #9333ea;
  --color-accent-nature-green-from: #10b981;
  --color-accent-nature-green-to: #059669;
  --color-accent-nature-lime-from: #84cc16;
  --color-accent-nature-lime-to: #16a34a;
  --color-accent-nature-emerald-from: #10b981;
  --color-accent-nature-emerald-to: #0d9488;
  --color-accent-neutral-gray-from: #6b7280;
  --color-accent-neutral-gray-to: #374151;
  --color-accent-neutral-amber-from: #d97706;
  --color-accent-neutral-amber-to: #92400e;

  /* Status colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

