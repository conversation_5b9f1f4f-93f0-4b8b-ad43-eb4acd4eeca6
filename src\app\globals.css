@import "tailwindcss";

@theme {
  /* Primary brand colors - Deep Navy & Gold Premium Palette */
  --color-primary-50: #f8fafc;
  --color-primary-100: #f1f5f9;
  --color-primary-200: #e2e8f0;
  --color-primary-300: #cbd5e1;
  --color-primary-400: #94a3b8;
  --color-primary-500: #64748b;
  --color-primary-600: #475569;
  --color-primary-700: #334155;
  --color-primary-800: #1e293b;
  --color-primary-900: #0f172a;
  --color-primary-950: #020617;

  /* Secondary brand colors - Luxury Gold Accents */
  --color-secondary-50: #fffdf7;
  --color-secondary-100: #fffaeb;
  --color-secondary-200: #fef3c7;
  --color-secondary-300: #fde68a;
  --color-secondary-400: #fcd34d;
  --color-secondary-500: #f59e0b;
  --color-secondary-600: #d97706;
  --color-secondary-700: #b45309;
  --color-secondary-800: #92400e;
  --color-secondary-900: #78350f;
  --color-secondary-950: #451a03;

  /* Background colors - Premium Dark Theme */
  --color-bg-primary: #0a0e1a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-card: rgba(30, 41, 59, 0.6);
  --color-bg-card-hover: rgba(51, 65, 85, 0.8);
  --color-bg-card-active: rgba(71, 85, 105, 0.9);
  --color-bg-overlay: rgba(10, 14, 26, 0.95);

  /* Text colors - Refined Typography */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #e2e8f0;
  --color-text-tertiary: #cbd5e1;
  --color-text-muted: #94a3b8;

  /* Premium accent colors for game cards */
  --color-accent-luxury-gold-from: #fbbf24;
  --color-accent-luxury-gold-to: #d97706;
  --color-accent-premium-bronze-from: #d97706;
  --color-accent-premium-bronze-to: #92400e;
  --color-accent-elegant-copper-from: #ea580c;
  --color-accent-elegant-copper-to: #c2410c;
  --color-accent-refined-amber-from: #f59e0b;
  --color-accent-refined-amber-to: #b45309;
  --color-accent-sophisticated-blue-from: #3b82f6;
  --color-accent-sophisticated-blue-to: #1e40af;
  --color-accent-premium-teal-from: #0d9488;
  --color-accent-premium-teal-to: #0f766e;
  --color-accent-luxury-indigo-from: #6366f1;
  --color-accent-luxury-indigo-to: #4338ca;
  --color-accent-elegant-cyan-from: #0891b2;
  --color-accent-elegant-cyan-to: #0e7490;
  --color-accent-premium-emerald-from: #059669;
  --color-accent-premium-emerald-to: #047857;
  --color-accent-sophisticated-green-from: #16a34a;
  --color-accent-sophisticated-green-to: #15803d;
  --color-accent-refined-lime-from: #65a30d;
  --color-accent-refined-lime-to: #4d7c0f;
  --color-accent-premium-slate-from: #64748b;
  --color-accent-premium-slate-to: #475569;
  --color-accent-luxury-rose-from: #e11d48;
  --color-accent-luxury-rose-to: #be123c;

  /* Status colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Hide scrollbar for Chrome, Safari and Opera */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

